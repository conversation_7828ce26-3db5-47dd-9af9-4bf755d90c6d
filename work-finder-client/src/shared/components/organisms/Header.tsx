import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { ChevronDown, Menu, X, Upload } from "lucide-react";
import { useAuthStore } from "@/features/authentication/stores/auth-store";
import { useClickOutside } from "@/shared/hooks";
import {
  PUBLIC_ROUTES,
  AUTH_ROUTES,
  DASHBOARD_ROUTES,
} from "@/constants/routes";

const navigationItems = [
  { label: "Home", href: PUBLIC_ROUTES.HOME },
  { 
    label: "Find Jobs", 
    href: PUBLIC_ROUTES.JOBS,
    hasDropdown: true
  },
  { 
    label: "Employers", 
    href: PUBLIC_ROUTES.COMPANIES,
    hasDropdown: true
  },
  { 
    label: "Candidates", 
    href: "/candidates",
    hasDropdown: true
  },
  { 
    label: "Blog", 
    href: "/blog",
    hasDropdown: true
  },
  { 
    label: "Pages", 
    href: "/pages",
    hasDropdown: true
  },
];

export function Header() {
  const { isAuthenticated, user, logout } = useAuthStore();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  
  const dropdownRef = useClickOutside<HTMLDivElement>(() => {
    setIsUserDropdownOpen(false);
  });

  const handleLogout = async () => {
    await logout();
    navigate(PUBLIC_ROUTES.HOME);
    setIsUserDropdownOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="bg-white border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-[70px]">
          {/* Logo */}
          <Link to={PUBLIC_ROUTES.HOME} className="flex items-center">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-[#1967D2] rounded-lg flex items-center justify-center mr-2">
                <div className="w-5 h-5 bg-white rounded-sm"></div>
              </div>
              <span className="text-[25px] font-bold font-['Jost']">
                <span className="text-[#1967D2]">Work</span>
                <span className="text-[#4a5568]">Finder</span>
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div key={item.label} className="relative group">
                <Link
                  to={item.href}
                  className="flex items-center space-x-1 text-[#202124] hover:text-[#1967D2] transition-colors font-['Jost'] text-[15px] font-normal py-2"
                >
                  <span>{item.label}</span>
                  {item.hasDropdown && (
                    <ChevronDown className="w-3 h-3 text-[#202124]" />
                  )}
                </Link>
              </div>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link
              to="/upload-cv"
              className="flex items-center space-x-2 text-[#1967D2] hover:text-[#1457b8] transition-colors font-['Jost'] text-[15px] font-normal"
            >
              <Upload className="w-4 h-4" />
              <span>Upload your CV</span>
            </Link>

            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                {/* User Dropdown */}
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                    className="flex items-center space-x-2 text-[#202124] hover:text-[#1967D2] transition-colors font-['Jost'] text-[15px] font-normal"
                  >
                    <div className="w-8 h-8 bg-[#1967D2] rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {user?.name?.charAt(0)?.toUpperCase() || "U"}
                      </span>
                    </div>
                    <span>{user?.name}</span>
                    <ChevronDown className="w-3 h-3" />
                  </button>

                  {/* Dropdown Menu */}
                  {isUserDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
                      <div className="py-2">
                        <Link
                          to={DASHBOARD_ROUTES.PROFILE}
                          className="block px-4 py-2 text-sm text-[#202124] hover:bg-gray-50 font-['Jost']"
                          onClick={() => setIsUserDropdownOpen(false)}
                        >
                          Complete Profile
                        </Link>
                        <Link
                          to={DASHBOARD_ROUTES.APPLICATIONS}
                          className="block px-4 py-2 text-sm text-[#202124] hover:bg-gray-50 font-['Jost']"
                          onClick={() => setIsUserDropdownOpen(false)}
                        >
                          My Applications
                        </Link>
                        <Link
                          to={DASHBOARD_ROUTES.SAVED_JOBS}
                          className="block px-4 py-2 text-sm text-[#202124] hover:bg-gray-50 font-['Jost']"
                          onClick={() => setIsUserDropdownOpen(false)}
                        >
                          Saved Jobs
                        </Link>
                        <div className="border-t border-gray-100 my-1"></div>
                        <button
                          onClick={handleLogout}
                          className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-50 font-['Jost']"
                        >
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <Link
                  to="/post-job"
                  className="bg-[#1967D2] text-white px-6 py-2 rounded-lg hover:bg-[#1457b8] transition-colors font-['Jost'] text-[15px] font-normal"
                >
                  Job Post
                </Link>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  to="/upload-cv"
                  className="bg-[rgba(25,103,210,0.07)] text-[#1967D2] px-6 py-2 rounded-lg hover:bg-[rgba(25,103,210,0.1)] transition-colors font-['Jost'] text-[15px] font-normal"
                >
                  Login / Register
                </Link>
                <Link
                  to="/post-job"
                  className="bg-[#1967D2] text-white px-6 py-2 rounded-lg hover:bg-[#1457b8] transition-colors font-['Jost'] text-[15px] font-normal"
                >
                  Job Post
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileMenu}
            className="lg:hidden p-2 text-[#202124] hover:text-[#1967D2] transition-colors"
          >
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-100">
            <div className="py-4 space-y-4">
              {/* Mobile Navigation */}
              {navigationItems.map((item) => (
                <Link
                  key={item.label}
                  to={item.href}
                  className="block text-[#202124] hover:text-[#1967D2] transition-colors font-['Jost'] text-[15px] font-normal py-2"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}

              {/* Mobile Actions */}
              <div className="pt-4 border-t border-gray-100 space-y-3">
                <Link
                  to="/upload-cv"
                  className="flex items-center space-x-2 text-[#1967D2] hover:text-[#1457b8] transition-colors font-['Jost'] text-[15px] font-normal"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Upload className="w-4 h-4" />
                  <span>Upload your CV</span>
                </Link>

                {isAuthenticated ? (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-[#202124] font-['Jost'] text-[15px] font-normal">
                      <div className="w-8 h-8 bg-[#1967D2] rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {user?.name?.charAt(0)?.toUpperCase() || "U"}
                        </span>
                      </div>
                      <span>{user?.name}</span>
                    </div>
                    <Link
                      to={DASHBOARD_ROUTES.PROFILE}
                      className="block text-[#202124] hover:text-[#1967D2] transition-colors font-['Jost'] text-[15px] font-normal py-1"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Complete Profile
                    </Link>
                    <Link
                      to={DASHBOARD_ROUTES.APPLICATIONS}
                      className="block text-[#202124] hover:text-[#1967D2] transition-colors font-['Jost'] text-[15px] font-normal py-1"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      My Applications
                    </Link>
                    <Link
                      to={DASHBOARD_ROUTES.SAVED_JOBS}
                      className="block text-[#202124] hover:text-[#1967D2] transition-colors font-['Jost'] text-[15px] font-normal py-1"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Saved Jobs
                    </Link>
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsMobileMenuOpen(false);
                      }}
                      className="block w-full text-left text-red-600 hover:text-red-700 transition-colors font-['Jost'] text-[15px] font-normal py-1"
                    >
                      Sign Out
                    </button>
                    <Link
                      to="/post-job"
                      className="block bg-[#1967D2] text-white px-6 py-2 rounded-lg text-center hover:bg-[#1457b8] transition-colors font-['Jost'] text-[15px] font-normal"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Job Post
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Link
                      to={AUTH_ROUTES.LOGIN}
                      className="block bg-[rgba(25,103,210,0.07)] text-[#1967D2] px-6 py-2 rounded-lg text-center hover:bg-[rgba(25,103,210,0.1)] transition-colors font-['Jost'] text-[15px] font-normal"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Login / Register
                    </Link>
                    <Link
                      to="/post-job"
                      className="block bg-[#1967D2] text-white px-6 py-2 rounded-lg text-center hover:bg-[#1457b8] transition-colors font-['Jost'] text-[15px] font-normal"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Job Post
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
