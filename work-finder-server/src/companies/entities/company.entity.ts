import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { JobPost } from '../../jobs/entities/job-post.entity';
import { FollowedCompany } from './followed-company.entity';

@Entity('companies')
export class Company {
  @PrimaryGeneratedColumn()
  company_id: number;

  @Column({ name: 'company_name', length: 200 })
  company_name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'company_image', length: 255, nullable: true })
  company_image?: string;

  @Column({ name: 'industry', length: 100, nullable: true })
  industry?: string;

  @Column({ name: 'website', length: 255, nullable: true })
  website?: string;

  @Column({ name: 'address', type: 'text', nullable: true })
  address?: string;

  @Column({ name: 'location', length: 100, nullable: true })
  location?: string;

  @Column({ name: 'company_size', length: 20, nullable: true })
  company_size?: string;

  @Column({ name: 'is_verified', default: false })
  is_verified: boolean;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relations
  @OneToMany(() => JobPost, (jobPost) => jobPost.company)
  job_posts: JobPost[];

  @OneToMany(() => FollowedCompany, (followedCompany) => followedCompany.company)
  followers: FollowedCompany[];
}
