import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';

// Load environment variables
config();

const configService = new ConfigService();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: configService.get('DB_HOST') || 'localhost',
  port: parseInt(configService.get('DB_PORT') || '5432'),
  username: configService.get('DB_USERNAME') || 'postgres',
  password: configService.get('DB_PASSWORD') || 'password',
  database: configService.get('DB_DATABASE') || 'work_finder',
  entities: [
    __dirname + '/../users/entities/user.entity{.ts,.js}',
    __dirname + '/../companies/entities/company.entity{.ts,.js}',
    __dirname + '/../jobs/entities/job.entity{.ts,.js}', 
    __dirname + '/../applications/entities/application.entity{.ts,.js}',
    __dirname + '/../applications/entities/interview.entity{.ts,.js}',
    __dirname + '/../jobs/entities/saved-job.entity{.ts,.js}',
    __dirname + '/../companies/entities/followed-company.entity{.ts,.js}',
    __dirname + '/../notifications/entities/notification.entity{.ts,.js}',
    __dirname + '/../resumes/entities/resume.entity{.ts,.js}',
  ],
  migrations: [__dirname + '/migrations/*{.ts,.js}'],
  synchronize: true, // Force synchronize to recreate schema
  logging: configService.get('NODE_ENV') === 'development',
  ssl:
    configService.get('NODE_ENV') === 'production'
      ? { rejectUnauthorized: false }
      : false,
});
