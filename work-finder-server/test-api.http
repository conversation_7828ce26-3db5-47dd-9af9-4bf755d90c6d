### Work Finder API Testing

### 1. Register a Job Seeker
POST http://localhost:3000/api/v1/auth/register
Content-Type: application/json

{
  "username": "john_seeker",
  "password": "password123",
  "full_name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "role": "job_seeker"
}

### 2. Register a Recruiter
POST http://localhost:3000/api/v1/auth/register
Content-Type: application/json

{
  "username": "jane_recruiter",
  "password": "password123",
  "full_name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+0987654321",
  "role": "recruiter"
}

### 3. Login as Job Seeker
POST http://localhost:3000/api/v1/auth/login
Content-Type: application/json

{
  "username": "john_seeker",
  "password": "password123"
}

### 4. Login as Recruiter
POST http://localhost:3000/api/v1/auth/login
Content-Type: application/json

{
  "username": "jane_recruiter",
  "password": "password123"
}

### 5. Get Current User Profile (requires JWT token)
POST http://localhost:3000/api/v1/auth/me
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 6. Create a Company (requires Recruiter/Admin role)
POST http://localhost:3000/api/v1/companies
Content-Type: application/json
Authorization: Bearer YOUR_RECRUITER_JWT_TOKEN_HERE

{
  "company_name": "Tech Corp Inc.",
  "description": "Leading technology company specializing in software development",
  "industry": "Technology",
  "website": "https://techcorp.com"
}

### 7. Create a Job Post (requires Recruiter/Admin role)
POST http://localhost:3000/api/v1/jobs
Content-Type: application/json
Authorization: Bearer YOUR_RECRUITER_JWT_TOKEN_HERE

{
  "company_id": 1,
  "job_title": "Senior Software Engineer",
  "description": "We are looking for an experienced software engineer to join our team...",
  "location": "San Francisco, CA",
  "salary": "$80,000 - $120,000",
  "job_type": "full_time",
  "status": "active"
}

### 8. Get All Jobs (public)
GET http://localhost:3000/api/v1/jobs

### 9. Search Jobs with Filters
GET http://localhost:3000/api/v1/jobs?search=software&location=San Francisco&job_type=full_time&page=1&limit=10

### 10. Upload Avatar (requires authentication)
POST http://localhost:3000/api/v1/upload/avatar
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: multipart/form-data

# Use form-data with key "file" and select an image file

### 11. Upload Resume (requires authentication)
POST http://localhost:3000/api/v1/upload/resume
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: multipart/form-data

# Use form-data with key "file" and select a PDF/Word document

### 12. Get My Resumes (requires authentication)
GET http://localhost:3000/api/v1/resumes/my-resumes
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 13. Apply for a Job (requires authentication)
POST http://localhost:3000/api/v1/applications
Content-Type: application/json
Authorization: Bearer YOUR_JOB_SEEKER_JWT_TOKEN_HERE

{
  "job_id": 1,
  "resume_id": 1
}

### 14. Get All Applications (requires authentication)
GET http://localhost:3000/api/v1/applications
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### 15. Get Company Jobs
GET http://localhost:3000/api/v1/companies/1/jobs

### 16. Refresh Token
POST http://localhost:3000/api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "YOUR_REFRESH_TOKEN_HERE"
}

### 17. Logout (requires authentication)
POST http://localhost:3000/api/v1/auth/logout
Authorization: Bearer YOUR_JWT_TOKEN_HERE
